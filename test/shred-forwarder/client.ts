import type { StreamState } from '../../src'
import { ShredForwarderClient } from '../../src/clients/shred-forwarder'

const client = new ShredForwarderClient('http://localhost:50051')
const stream = client.createStream()

stream.on('state', (state: StreamState) => console.log('state', state))
stream.on('error', (error: unknown) => console.log('error', error))
stream.on('closed', (isExplicitly: boolean) => console.log('closed', { isExplicitly }))
stream.on('waitForResubscribe', (delay: number) => console.log('waitForResubscribe', { delay }))
stream.on('resubscribe', (attempt: number, retriesLeft: number) => console.log('resubscribe', { attempt, retriesLeft }))
stream.on('resubscribed', () => console.log('resubscribed'))
stream.on('resubscribeCompleted', () => console.log('resubscribeCompleted'))
stream.on('circuitBreakerTripped', (lastTime: number) => console.log('circuitBreakerTripped', { lastTime }))
stream.on('resubscribeAbandoned', (reason: string) => console.log('resubscribeAbandoned', { reason }))
stream.on('data', (data: unknown) => console.log('data', data))

await stream.subscribe()
