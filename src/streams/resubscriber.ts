import type { ClientReadableStream } from '@grpc/grpc-js'
import { notNullish } from '@kdt310722/utils/common'
import { transform } from '@kdt310722/utils/function'
import { type Awaitable, abortable, sleep } from '@kdt310722/utils/promise'
import type { StreamWrapper } from './stream-wrapper'

export enum ResubscribeReason {
    ABORTED = 'ABORTED',
    NOT_ENABLED = 'NOT_ENABLED',
    CIRCUIT_BREAKER_TRIPPED = 'CIRCUIT_BREAKER_TRIPPED',
    ERROR = 'ERROR',
    MAX_RETRIES_EXCEEDED = 'MAX_RETRIES_EXCEEDED',
    EXPLICITLY_CLOSED = 'EXPLICITLY_CLOSED',
    SHOULD_RESUBSCRIBE_FN_ERROR = 'SHOULD_RESUBSCRIBE_FN_ERROR',
}

export interface ResubscriberOptions {
    enabled?: boolean
    retries?: number
    delay?: number
    backoff?: number
    jitter?: number
    maxDelay?: number
    autoReset?: boolean
    circuitBreakerTimeout?: number
    shouldResubscribe?: (error?: unknown) => boolean
}

export type ResubscriberEvents = {
    error: (error: unknown) => void
    waitForResubscribe: (delay: number) => void
    resubscribe: (attempt: number, retriesLeft: number) => void
    resubscribed: () => void
    circuitBreakerTripped: (lastResubscribeSuccessTime: number) => void
    resubscribeAbandoned: (reason: ResubscribeReason) => void
    resubscribeCompleted: () => void
}

export const DEFAULT_RESUBSCRIBER_OPTIONS: Required<ResubscriberOptions> = {
    enabled: true,
    retries: 5,
    delay: 1000,
    backoff: 2,
    jitter: 0,
    maxDelay: 10_000,
    autoReset: true,
    circuitBreakerTimeout: 60_000,
    shouldResubscribe: () => true,
}

export class Resubscriber<TResponse, TStream extends ClientReadableStream<TResponse> = ClientReadableStream<TResponse>> {
    protected readonly options: Required<ResubscriberOptions>

    protected isResubscribing = false
    protected attempts = 0
    protected lastSuccessTime?: number
    protected afterResubscribed?: () => Awaitable<void>

    public constructor(protected readonly stream: StreamWrapper<TResponse, TStream>, options: ResubscriberOptions = {}) {
        this.options = { ...DEFAULT_RESUBSCRIBER_OPTIONS, ...options }
    }

    public onResubscribed(afterResubscribed: () => Awaitable<void>) {
        this.afterResubscribed = afterResubscribed
    }

    public reset() {
        this.attempts = 0
        this.lastSuccessTime = undefined
    }

    public resubscribe(error?: unknown, signal?: AbortSignal) {
        if (this.isResubscribing) {
            return
        }

        this.isResubscribing = true

        const abandon = (reason: ResubscribeReason, error?: unknown) => {
            if (notNullish(error)) {
                this.stream.emit('error', error)
            }

            this.stream.emit('resubscribeAbandoned', reason)
            this.isResubscribing = false
        }

        if (signal?.aborted) {
            return abandon(ResubscribeReason.ABORTED, signal.reason)
        }

        try {
            if (!this.options.enabled || !this.options.shouldResubscribe(error)) {
                return abandon(ResubscribeReason.NOT_ENABLED)
            }
        } catch (error_) {
            return abandon(ResubscribeReason.SHOULD_RESUBSCRIBE_FN_ERROR, error_)
        }

        if (this.lastSuccessTime && Date.now() - this.lastSuccessTime < this.options.circuitBreakerTimeout) {
            this.stream.emit('circuitBreakerTripped', this.lastSuccessTime)

            return abandon(ResubscribeReason.CIRCUIT_BREAKER_TRIPPED)
        }

        const handleError = (error: unknown) => {
            this.stream.destroyAndReset()
            abandon(ResubscribeReason.ERROR, error)
        }

        this.process(signal).catch(handleError).finally(() => {
            this.isResubscribing = false
        })
    }

    protected async process(signal?: AbortSignal) {
        if (this.attempts >= this.options.retries) {
            this.stream.emit('resubscribeAbandoned', ResubscribeReason.MAX_RETRIES_EXCEEDED)

            return
        }

        const delay = this.getResubscribeDelay(++this.attempts)

        if (delay > 0) {
            this.stream.emit('waitForResubscribe', delay)
        }

        const isWaited = await abortable(sleep(delay), signal).then(() => this.stream.emit('resubscribe', this.attempts, this.options.retries - this.attempts)).catch(() => false)

        if (!isWaited) {
            this.stream.emit('resubscribeAbandoned', ResubscribeReason.ABORTED)

            return
        }

        await this.stream.subscribe(signal).then(() => this.stream.emit('resubscribed'))
        await this.afterResubscribed?.()

        this.lastSuccessTime = Date.now()

        if (this.options.autoReset) {
            this.attempts = 0
        }

        this.stream.emit('resubscribeCompleted')
    }

    protected getResubscribeDelay(attempts: number) {
        const baseDelay = Math.min(this.options.delay * this.options.backoff ** (attempts - 1), this.options.maxDelay)

        if (this.options.jitter > 0) {
            return transform(baseDelay * this.options.jitter, (jitter) => baseDelay - (jitter / 2) + (Math.random() * jitter))
        }

        return baseDelay
    }
}
